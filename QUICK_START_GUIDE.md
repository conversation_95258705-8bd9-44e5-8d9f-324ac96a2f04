# ESP32连接ESP8266快速开始指南

## 🎯 推荐方案：同一WiFi网络

这种方案可以让您**同时使用小智AI和ESP8266通信功能**。

### 第一步：设置ESP8266

1. **修改ESP8266代码**
   打开`esp8266_server_example.ino`，修改以下内容：
   ```cpp
   const char* ssid = "您的WiFi名称";        // 改为您家的WiFi名称
   const char* password = "您的WiFi密码";     // 改为您家的WiFi密码
   const bool useExistingWiFi = true;        // 确保这里是true
   ```

2. **上传代码到ESP8266**
   - 连接ESP8266到电脑
   - 上传修改后的代码
   - 打开串口监视器（115200波特率）

3. **记录ESP8266的IP地址**
   串口会显示类似信息：
   ```
   WiFi连接成功! IP地址: *************
   TCP服务器已启动，端口: 8080
   ```
   **记住这个IP地址**（例如：*************）

### 第二步：配置ESP32

1. **确保ESP32已连接WiFi**
   - ESP32应该已经连接到与ESP8266相同的WiFi网络
   - 小智AI应该正常工作

2. **配置ESP8266的IP地址**
   对小智说：
   ```
   "配置ESP8266连接IP为*************端口为8080"
   ```
   （将*************替换为您在第一步记录的实际IP地址）

### 第三步：测试连接

1. **启动连接**
   对小智说：
   ```
   "连接ESP8266"
   ```

2. **查看连接状态**
   - ESP32串口应显示：`TCP连接成功`
   - ESP8266串口应显示：`ESP32客户端已连接!`

3. **发送测试消息**
   对小智说：
   ```
   "发送消息到ESP8266"
   ```

4. **断开连接**（可选）
   对小智说：
   ```
   "断开ESP8266"
   ```

## 🔧 故障排除

### 问题1：ESP8266连接WiFi失败
- 检查WiFi名称和密码是否正确
- 确保ESP8266在WiFi信号覆盖范围内
- 重启ESP8266重试

### 问题2：ESP32找不到ESP8266
- 确认ESP32和ESP8266在同一WiFi网络
- 检查IP地址配置是否正确
- 尝试ping ESP8266的IP地址

### 问题3：语音命令无响应
- 确认小智AI正常工作
- 尝试说"ESP8266状态"查看设备状态
- 检查设备是否正确注册

### 问题4：TCP连接失败
- 确认ESP8266服务器正在运行
- 检查端口8080是否被占用
- 查看两端的串口日志

## 📱 常用语音命令

```
"连接ESP8266"                    # 建立连接
"断开ESP8266"                    # 断开连接
"发送消息到ESP8266"              # 发送测试消息
"ESP8266状态"                    # 查看状态
"配置ESP8266连接IP为X.X.X.X端口为8080"  # 配置IP
"重置ESP8266配置"                # 重置为默认配置
```

## ✅ 成功标志

当一切正常工作时，您会看到：

**ESP32串口**：
```
I ESP8266Client: TCP连接成功
I ESP8266Client: 发送数据: Hello from ESP32!
I ESP8266Client: 接收数据: ESP8266收到: Hello from ESP32!
```

**ESP8266串口**：
```
ESP32客户端已连接!
客户端IP: 192.168.1.xxx
收到消息: Hello from ESP32!
发送回复: ESP8266收到: Hello from ESP32!
```

## 🎉 完成！

现在您可以：
- 继续正常使用小智AI的所有功能
- 通过语音命令控制ESP8266
- 在ESP32和ESP8266之间传输数据
- 基于这个框架开发更复杂的应用

如果遇到问题，请查看详细的`ESP8266_CONNECTION_README.md`文档。
