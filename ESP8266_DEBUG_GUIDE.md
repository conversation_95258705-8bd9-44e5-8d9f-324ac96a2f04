# ESP8266连接调试指南

## 问题诊断

如果语音控制ESP8266连接不起作用，请按以下步骤排查：

### 1. 检查设备注册

确认ESP8266设备已正确注册到IoT系统中：

**查看串口日志**，启动时应该看到：
```
I (xxx) ESP8266Client: ESP8266Client初始化完成
```

### 2. 测试语音命令

尝试以下语音命令（按优先级排序）：

**基础连接命令**：
- "连接ESP8266"
- "启动ESP8266连接" 
- "打开ESP8266"

**状态查询命令**：
- "ESP8266状态"
- "查看ESP8266连接状态"

**断开连接命令**：
- "断开ESP8266"
- "关闭ESP8266连接"

### 3. 检查ESP8266服务器

确保ESP8266端正常运行：

1. **上传服务器代码**：将`esp8266_server_example.ino`上传到ESP8266
2. **检查串口输出**：应该看到类似信息：
   ```
   热点IP地址: ***********
   热点SSID: ESP8266_AP
   TCP服务器已启动，端口: 8080
   等待ESP32客户端连接...
   ```

### 4. 网络连接检查

**重要**：ESP32需要断开当前WiFi并连接到ESP8266热点

当前实现会：
1. 停止ESP32的现有WiFi连接
2. 连接到ESP8266创建的热点(ESP8266_AP)
3. 建立TCP连接到***********:8080

### 5. 串口日志分析

**ESP32端日志关键信息**：
```
I (xxx) ESP8266Client: ESP8266客户端已启动，正在连接到热点: ESP8266_AP
I (xxx) ESP8266Client: 开始连接ESP8266热点
I (xxx) ESP8266Client: 获取IP地址: 192.168.4.x
I (xxx) ESP8266Client: 连接到服务器: ***********:8080
I (xxx) ESP8266Client: TCP连接成功
```

**ESP8266端日志关键信息**：
```
ESP32客户端已连接!
客户端IP: 192.168.4.x
收到消息: Hello from ESP32!
```

### 6. 常见问题解决

**问题1：语音命令无响应**
- 检查设备名称是否正确注册
- 尝试更简单的命令："连接ESP8266"
- 查看AI服务器是否识别了命令

**问题2：WiFi连接失败**
- 确认ESP8266热点正常工作
- 检查SSID和密码是否正确
- 确认ESP32和ESP8266距离足够近

**问题3：TCP连接失败**
- 确认ESP8266服务器正在监听8080端口
- 检查IP地址配置(***********)
- 确认防火墙没有阻止连接

### 7. 手动测试方法

如果语音命令不工作，可以通过其他方式测试：

1. **通过MQTT/WebSocket直接发送命令**（需要开发工具）
2. **修改代码添加按钮触发**（临时调试）
3. **使用串口命令**（如果有实现）

### 8. 配置检查

检查当前配置：
```cpp
// 默认配置
IP: ***********
Port: 8080
SSID: ESP8266_AP
Password: password123
```

### 9. 代码修改建议

如果仍然不工作，可以尝试以下修改：

**临时调试代码**（添加到ESP8266Client构造函数）：
```cpp
// 添加自动启动（仅用于调试）
ESP_LOGI(TAG, "自动启动ESP8266连接（调试模式）");
StartConnection();
```

**简化设备名称**：
当前使用的设备名称是"ESP8266"，AI应该能够识别。

### 10. 下一步调试

如果以上步骤都无效，请：

1. 提供完整的串口日志
2. 确认语音命令是否被AI识别
3. 检查IoT命令分发是否正常
4. 验证ESP8266服务器是否可以被其他设备连接

## 成功连接的标志

当一切正常工作时，您应该看到：

**ESP32串口**：
```
I ESP8266Client: TCP连接成功
I ESP8266Client: 发送数据: Hello from ESP32!
I ESP8266Client: 接收数据: Hello from ESP8266 Server!
```

**ESP8266串口**：
```
ESP32客户端已连接!
收到消息: Hello from ESP32!
发送回复: ESP8266收到: Hello from ESP32!
```

这表明ESP32和ESP8266之间的通信已经建立成功。
