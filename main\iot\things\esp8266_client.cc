#include "iot/thing.h"
#include "board.h"
#include "application.h"
#include "wifi_station.h"
#include "settings.h"

#include <esp_log.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <lwip/err.h>
#include <lwip/sockets.h>
#include <lwip/sys.h>
#include <lwip/netdb.h>
#include <lwip/dns.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <string.h>

#define TAG "ESP8266Client"

// ESP8266 AP配置
#define ESP8266_SSID "ESP8266_AP"
#define ESP8266_PASSWORD "password123"
#define ESP8266_SERVER_IP "***********"
#define ESP8266_SERVER_PORT 8080

// 事件组位定义
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT BIT1

namespace iot {

class ESP8266Client : public Thing {
private:
    bool connected_ = false;
    bool enabled_ = false;
    int socket_fd_ = -1;
    std::string last_message_;
    std::string connection_status_;
    std::string esp8266_ssid_ = ESP8266_SSID;
    std::string esp8266_password_ = ESP8266_PASSWORD;
    std::string esp8266_ip_ = ESP8266_SERVER_IP;
    int esp8266_port_ = ESP8266_SERVER_PORT;

    TaskHandle_t tcp_task_handle_ = nullptr;
    bool use_existing_wifi_ = true; // 默认使用现有WiFi网络（推荐）

    EventGroupHandle_t wifi_event_group_;
    esp_event_handler_instance_t instance_any_id_;
    esp_event_handler_instance_t instance_got_ip_;
    bool wifi_connected_ = false;
    
    // WiFi事件处理器
    static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                                   int32_t event_id, void* event_data) {
        ESP8266Client* client = static_cast<ESP8266Client*>(arg);

        if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
            esp_wifi_connect();
            ESP_LOGI(TAG, "开始连接ESP8266热点");
        } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
            ESP_LOGI(TAG, "与ESP8266热点断开连接，尝试重连");
            client->connection_status_ = "连接断开，重连中...";
            client->wifi_connected_ = false;
            esp_wifi_connect();
            xEventGroupClearBits(client->wifi_event_group_, WIFI_CONNECTED_BIT);
        } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
            ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
            ESP_LOGI(TAG, "获取IP地址: " IPSTR, IP2STR(&event->ip_info.ip));
            client->connection_status_ = "WiFi已连接到ESP8266";
            client->wifi_connected_ = true;
            xEventGroupSetBits(client->wifi_event_group_, WIFI_CONNECTED_BIT);
        }
    }

    // 检查WiFi连接状态
    bool IsWiFiConnected() {
        if (use_existing_wifi_) {
            auto& wifi_station = WifiStation::GetInstance();
            return wifi_station.IsConnected();
        } else {
            return wifi_connected_;
        }
    }
    
    // TCP客户端任务
    static void tcp_client_task(void* pvParameters) {
        ESP8266Client* client = static_cast<ESP8266Client*>(pvParameters);
        client->RunTcpClient();
        vTaskDelete(NULL);
    }
    
    void RunTcpClient() {
        char rx_buffer[128];

        while (enabled_) {
            // 等待WiFi连接
            if (!IsWiFiConnected()) {
                ESP_LOGW(TAG, "等待WiFi连接...");
                connection_status_ = "等待WiFi连接";
                vTaskDelay(2000 / portTICK_PERIOD_MS);
                continue;
            }
            
            // 创建TCP套接字
            struct sockaddr_in dest_addr;
            dest_addr.sin_addr.s_addr = inet_addr(esp8266_ip_.c_str());
            dest_addr.sin_family = AF_INET;
            dest_addr.sin_port = htons(esp8266_port_);
            
            socket_fd_ = socket(AF_INET, SOCK_STREAM, IPPROTO_IP);
            if (socket_fd_ < 0) {
                ESP_LOGE(TAG, "创建套接字失败");
                connection_status_ = "套接字创建失败";
                vTaskDelay(2000 / portTICK_PERIOD_MS);
                continue;
            }
            
            ESP_LOGI(TAG, "连接到服务器: %s:%d", esp8266_ip_.c_str(), esp8266_port_);
            connection_status_ = "正在连接ESP8266...";
            
            int err = connect(socket_fd_, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
            if (err != 0) {
                ESP_LOGE(TAG, "连接失败: %d", errno);
                connection_status_ = "TCP连接失败";
                close(socket_fd_);
                socket_fd_ = -1;
                vTaskDelay(2000 / portTICK_PERIOD_MS);
                continue;
            }
            
            ESP_LOGI(TAG, "TCP连接成功");
            connected_ = true;
            connection_status_ = "已连接到ESP8266";
            
            // 发送初始消息
            const char* hello_msg = "Hello from ESP32!";
            send(socket_fd_, hello_msg, strlen(hello_msg), 0);
            ESP_LOGI(TAG, "发送数据: %s", hello_msg);
            
            // 接收数据循环
            while (enabled_ && connected_) {
                int len = recv(socket_fd_, rx_buffer, sizeof(rx_buffer) - 1, 0);
                
                if (len < 0) {
                    ESP_LOGE(TAG, "接收失败");
                    break;
                } else if (len == 0) {
                    ESP_LOGI(TAG, "连接已关闭");
                    break;
                } else {
                    rx_buffer[len] = 0;
                    last_message_ = std::string(rx_buffer);
                    ESP_LOGI(TAG, "接收数据: %s", rx_buffer);
                }
            }
            
            // 清理连接
            connected_ = false;
            connection_status_ = "连接已断开";
            close(socket_fd_);
            socket_fd_ = -1;
            ESP_LOGI(TAG, "TCP连接已关闭");
            
            if (enabled_) {
                vTaskDelay(5000 / portTICK_PERIOD_MS);
            }
        }
    }
    
    void StartConnection() {
        if (enabled_) {
            return; // 已经启动
        }

        enabled_ = true;

        if (use_existing_wifi_) {
            // 方案1：使用现有WiFi网络（推荐）
            connection_status_ = "正在启动TCP连接...";
            ESP_LOGI(TAG, "使用现有WiFi连接，目标: %s:%d", esp8266_ip_.c_str(), esp8266_port_);

            // 直接创建TCP客户端任务
            xTaskCreate(tcp_client_task, "esp8266_tcp_client", 4096, this, 5, &tcp_task_handle_);
        } else {
            // 方案2：连接到ESP8266热点（备选）
            connection_status_ = "正在启动WiFi连接...";

            // 先停止现有的WiFi连接
            esp_wifi_stop();
            vTaskDelay(1000 / portTICK_PERIOD_MS);

            // 创建事件组
            wifi_event_group_ = xEventGroupCreate();

            // 注册WiFi事件处理器
            ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                                ESP_EVENT_ANY_ID,
                                                                &wifi_event_handler,
                                                                this,
                                                                &instance_any_id_));
            ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                                IP_EVENT_STA_GOT_IP,
                                                                &wifi_event_handler,
                                                                this,
                                                                &instance_got_ip_));

            // 配置WiFi连接到ESP8266热点
            wifi_config_t wifi_config = {};
            strcpy((char*)wifi_config.sta.ssid, esp8266_ssid_.c_str());
            strcpy((char*)wifi_config.sta.password, esp8266_password_.c_str());
            wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;

            ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
            ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
            ESP_ERROR_CHECK(esp_wifi_start());

            // 创建TCP客户端任务
            xTaskCreate(tcp_client_task, "esp8266_tcp_client", 4096, this, 5, &tcp_task_handle_);

            ESP_LOGI(TAG, "ESP8266客户端已启动，正在连接到热点: %s", esp8266_ssid_.c_str());
        }
    }
    
    void StopConnection() {
        if (!enabled_) {
            return; // 已经停止
        }

        enabled_ = false;
        connected_ = false;
        wifi_connected_ = false;
        connection_status_ = "已停止";

        // 关闭套接字
        if (socket_fd_ >= 0) {
            close(socket_fd_);
            socket_fd_ = -1;
        }

        // 等待任务结束
        if (tcp_task_handle_ != nullptr) {
            vTaskDelete(tcp_task_handle_);
            tcp_task_handle_ = nullptr;
        }

        if (!use_existing_wifi_) {
            // 只有在使用独立WiFi模式时才停止WiFi
            esp_wifi_stop();

            // 注销事件处理器
            if (instance_any_id_) {
                esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, instance_any_id_);
                instance_any_id_ = nullptr;
            }
            if (instance_got_ip_) {
                esp_event_handler_instance_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, instance_got_ip_);
                instance_got_ip_ = nullptr;
            }

            // 删除事件组
            if (wifi_event_group_) {
                vEventGroupDelete(wifi_event_group_);
                wifi_event_group_ = nullptr;
            }
        }

        ESP_LOGI(TAG, "ESP8266客户端已停止");
    }

    void LoadConfiguration() {
        Settings settings("esp8266", true);
        esp8266_ip_ = settings.GetString("ip", ESP8266_SERVER_IP);
        esp8266_port_ = settings.GetInt("port", ESP8266_SERVER_PORT);
        esp8266_ssid_ = settings.GetString("ssid", ESP8266_SSID);
        esp8266_password_ = settings.GetString("password", ESP8266_PASSWORD);
        use_existing_wifi_ = settings.GetInt("use_existing_wifi", 1) == 1; // 默认使用现有WiFi

        ESP_LOGI(TAG, "加载配置: IP=%s, Port=%d, UseExistingWiFi=%s",
                 esp8266_ip_.c_str(), esp8266_port_, use_existing_wifi_ ? "是" : "否");
    }

    void SaveConfiguration() {
        Settings settings("esp8266", true);
        settings.SetString("ip", esp8266_ip_);
        settings.SetInt("port", esp8266_port_);
        settings.SetString("ssid", esp8266_ssid_);
        settings.SetString("password", esp8266_password_);
        settings.SetInt("use_existing_wifi", use_existing_wifi_ ? 1 : 0);

        ESP_LOGI(TAG, "保存配置: IP=%s, Port=%d, UseExistingWiFi=%s",
                 esp8266_ip_.c_str(), esp8266_port_, use_existing_wifi_ ? "是" : "否");
    }

public:
    ESP8266Client() : Thing("ESP8266", "ESP8266设备连接") {
        // 加载配置
        LoadConfiguration();

        // 定义设备属性
        properties_.AddBooleanProperty("connected", "是否已连接到ESP8266", [this]() -> bool {
            return connected_;
        });

        properties_.AddBooleanProperty("enabled", "是否启用ESP8266连接", [this]() -> bool {
            return enabled_;
        });

        properties_.AddStringProperty("status", "连接状态", [this]() -> std::string {
            return connection_status_;
        });

        properties_.AddStringProperty("last_message", "最后接收的消息", [this]() -> std::string {
            return last_message_;
        });

        properties_.AddStringProperty("target_ip", "目标IP地址", [this]() -> std::string {
            return esp8266_ip_;
        });

        properties_.AddNumberProperty("target_port", "目标端口", [this]() -> int {
            return esp8266_port_;
        });

        // 定义设备方法
        methods_.AddMethod("Connect", "连接到ESP8266", ParameterList(), [this](const ParameterList& parameters) {
            StartConnection();
        });

        methods_.AddMethod("Disconnect", "断开ESP8266连接", ParameterList(), [this](const ParameterList& parameters) {
            StopConnection();
        });

        // 发送消息方法
        ParameterList send_params;
        send_params.AddParameter(Parameter("message", "要发送的消息", kValueTypeString));
        methods_.AddMethod("SendMessage", "发送消息到ESP8266", send_params, [this](const ParameterList& parameters) {
            if (connected_ && socket_fd_ >= 0) {
                std::string message = parameters["message"].string();
                int result = send(socket_fd_, message.c_str(), message.length(), 0);
                if (result >= 0) {
                    ESP_LOGI(TAG, "发送消息: %s", message.c_str());
                } else {
                    ESP_LOGE(TAG, "发送消息失败");
                }
            } else {
                ESP_LOGW(TAG, "未连接，无法发送消息");
            }
        });

        // 配置ESP8266连接参数
        ParameterList config_params;
        config_params.AddParameter(Parameter("ip", "ESP8266的IP地址", kValueTypeString));
        config_params.AddParameter(Parameter("port", "ESP8266的端口号", kValueTypeNumber));
        methods_.AddMethod("Configure", "配置ESP8266连接参数", config_params, [this](const ParameterList& parameters) {
            esp8266_ip_ = parameters["ip"].string();
            esp8266_port_ = parameters["port"].number();
            SaveConfiguration();
            ESP_LOGI(TAG, "配置ESP8266连接: %s:%d", esp8266_ip_.c_str(), esp8266_port_);
        });

        // 重置配置到默认值
        methods_.AddMethod("ResetConfig", "重置配置到默认值", ParameterList(), [this](const ParameterList& parameters) {
            esp8266_ip_ = ESP8266_SERVER_IP;
            esp8266_port_ = ESP8266_SERVER_PORT;
            esp8266_ssid_ = ESP8266_SSID;
            esp8266_password_ = ESP8266_PASSWORD;
            use_existing_wifi_ = true; // 默认使用现有WiFi
            SaveConfiguration();
            ESP_LOGI(TAG, "配置已重置到默认值");
        });

        // 切换WiFi模式
        ParameterList wifi_mode_params;
        wifi_mode_params.AddParameter(Parameter("use_existing", "是否使用现有WiFi", kValueTypeBoolean));
        methods_.AddMethod("SetWiFiMode", "设置WiFi连接模式", wifi_mode_params, [this](const ParameterList& parameters) {
            use_existing_wifi_ = parameters["use_existing"].boolean();
            SaveConfiguration();
            ESP_LOGI(TAG, "WiFi模式已设置为: %s", use_existing_wifi_ ? "使用现有WiFi" : "连接ESP8266热点");
        });

        ESP_LOGI(TAG, "ESP8266Client初始化完成");
    }
    
    ~ESP8266Client() {
        StopConnection();
    }
};

} // namespace iot

DECLARE_THING(ESP8266Client);
