/*
 * ESP8266 TCP服务器示例代码
 * 用于测试ESP32客户端连接
 * 
 * 硬件要求：ESP8266开发板
 * 功能：创建WiFi热点，启动TCP服务器，接收ESP32发送的消息
 */

#include <ESP8266WiFi.h>
#include <WiFiServer.h>
#include <WiFiClient.h>

// WiFi热点配置
const char* ssid = "ESP8266_AP";
const char* password = "password123";

// TCP服务器配置
const int serverPort = 8080;

// 注意：如果需要设置特定IP地址(如*************)，请在setup()中添加：
// WiFi.softAPConfig(IPAddress(192, 168, 254, 8), IPAddress(192, 168, 254, 1), IPAddress(255, 255, 255, 0));
WiFiServer server(serverPort);

// 客户端连接
WiFiClient client;
bool clientConnected = false;

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println();
  Serial.println("ESP8266 TCP服务器启动中...");
  
  // 配置WiFi热点
  WiFi.mode(WIFI_AP);

  // 设置自定义IP地址
  IPAddress local_IP(192, 168, 254, 8);
  IPAddress gateway(192, 168, 254, 1);
  IPAddress subnet(255, 255, 255, 0);
  WiFi.softAPConfig(local_IP, gateway, subnet);

  WiFi.softAP(ssid, password);
  
  // 获取热点IP地址
  IPAddress IP = WiFi.softAPIP();
  Serial.print("热点IP地址: ");
  Serial.println(IP);
  Serial.print("热点SSID: ");
  Serial.println(ssid);
  Serial.print("热点密码: ");
  Serial.println(password);
  
  // 启动TCP服务器
  server.begin();
  Serial.print("TCP服务器已启动，端口: ");
  Serial.println(serverPort);
  Serial.println("等待ESP32客户端连接...");
}

void loop() {
  // 检查新的客户端连接
  if (!clientConnected) {
    client = server.available();
    if (client) {
      clientConnected = true;
      Serial.println("ESP32客户端已连接!");
      Serial.print("客户端IP: ");
      Serial.println(client.remoteIP());
      
      // 发送欢迎消息
      client.println("Hello from ESP8266 Server!");
    }
  }
  
  // 处理已连接的客户端
  if (clientConnected && client.connected()) {
    // 检查是否有数据可读
    if (client.available()) {
      String message = client.readString();
      message.trim();
      Serial.print("收到消息: ");
      Serial.println(message);
      
      // 回复消息
      String response = "ESP8266收到: " + message;
      client.println(response);
      Serial.print("发送回复: ");
      Serial.println(response);
    }
  } else if (clientConnected) {
    // 客户端断开连接
    Serial.println("ESP32客户端已断开连接");
    clientConnected = false;
    client.stop();
  }
  
  delay(100);
}

/*
 * 使用说明：
 * 1. 将此代码上传到ESP8266开发板
 * 2. 打开串口监视器查看热点信息
 * 3. 在ESP32上运行小智项目
 * 4. 通过语音命令控制ESP8266连接：
 *    - "连接ESP8266" - 启动连接
 *    - "断开ESP8266" - 断开连接
 *    - "发送消息到ESP8266" - 发送测试消息
 * 
 * 注意事项：
 * - 确保ESP8266和ESP32在同一网络环境中
 * - 可以根据需要修改SSID、密码和端口配置
 * - 串口监视器波特率设置为115200
 */
