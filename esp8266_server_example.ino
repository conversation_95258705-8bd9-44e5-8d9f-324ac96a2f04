/*
 * ESP8266 TCP服务器示例代码
 * 用于测试ESP32客户端连接
 * 
 * 硬件要求：ESP8266开发板
 * 功能：创建WiFi热点，启动TCP服务器，接收ESP32发送的消息
 */

#include <ESP8266WiFi.h>
#include <WiFiServer.h>
#include <WiFiClient.h>

// WiFi配置 - 连接到现有网络（推荐方案）
const char* ssid = "Redmi K70";        // 替换为您的WiFi名称
const char* password = "88888888"; // 替换为您的WiFi密码

// 或者使用热点模式（备选方案）
const char* ap_ssid = "ESP8266_AP";
const char* ap_password = "password123";

// TCP服务器配置
const int serverPort = 8080;

// 选择工作模式：true=连接现有WiFi, false=创建热点
const bool useExistingWiFi = true;
WiFiServer server(serverPort);

// 客户端连接
WiFiClient client;
bool clientConnected = false;

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println();
  Serial.println("ESP8266 TCP服务器启动中...");
  
  if (useExistingWiFi) {
    // 方案1：连接到现有WiFi网络（推荐）
    Serial.println("连接到现有WiFi网络...");
    WiFi.mode(WIFI_STA);
    WiFi.begin(ssid, password);

    // 等待连接
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      Serial.print(".");
      attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
      Serial.println();
      Serial.print("WiFi连接成功! IP地址: ");
      Serial.println(WiFi.localIP());
    } else {
      Serial.println();
      Serial.println("WiFi连接失败，切换到热点模式");
      // 如果连接失败，切换到热点模式
      WiFi.mode(WIFI_AP);
      WiFi.softAP(ap_ssid, ap_password);
      Serial.print("热点模式启动，IP地址: ");
      Serial.println(WiFi.softAPIP());
    }
  } else {
    // 方案2：创建WiFi热点
    Serial.println("创建WiFi热点...");
    WiFi.mode(WIFI_AP);
    WiFi.softAP(ap_ssid, ap_password);
    Serial.print("热点IP地址: ");
    Serial.println(WiFi.softAPIP());
  }
  
  // 获取热点IP地址
  IPAddress IP = WiFi.softAPIP();
  Serial.print("热点IP地址: ");
  Serial.println(IP);
  Serial.print("热点SSID: ");
  Serial.println(ssid);
  Serial.print("热点密码: ");
  Serial.println(password);
  
  // 启动TCP服务器
  server.begin();
  Serial.print("TCP服务器已启动，端口: ");
  Serial.println(serverPort);
  Serial.println("等待ESP32客户端连接...");
}

void loop() {
  // 检查新的客户端连接
  if (!clientConnected) {
    client = server.available();
    if (client) {
      clientConnected = true;
      Serial.println("ESP32客户端已连接!");
      Serial.print("客户端IP: ");
      Serial.println(client.remoteIP());
      
      // 发送欢迎消息
      client.println("Hello from ESP8266 Server!");
    }
  }
  
  // 处理已连接的客户端
  if (clientConnected && client.connected()) {
    // 检查是否有数据可读
    if (client.available()) {
      String message = client.readString();
      message.trim();
      Serial.print("收到消息: ");
      Serial.println(message);
      
      // 回复消息
      String response = "ESP8266收到: " + message;
      client.println(response);
      Serial.print("发送回复: ");
      Serial.println(response);
    }
  } else if (clientConnected) {
    // 客户端断开连接
    Serial.println("ESP32客户端已断开连接");
    clientConnected = false;
    client.stop();
  }
  
  delay(100);
}

/*
 * 使用说明：
 * 1. 将此代码上传到ESP8266开发板
 * 2. 打开串口监视器查看热点信息
 * 3. 在ESP32上运行小智项目
 * 4. 通过语音命令控制ESP8266连接：
 *    - "连接ESP8266" - 启动连接
 *    - "断开ESP8266" - 断开连接
 *    - "发送消息到ESP8266" - 发送测试消息
 * 
 * 注意事项：
 * - 确保ESP8266和ESP32在同一网络环境中
 * - 可以根据需要修改SSID、密码和端口配置
 * - 串口监视器波特率设置为115200
 */
